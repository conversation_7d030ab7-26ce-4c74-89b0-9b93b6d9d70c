package qhyu.asia.swcares.ui

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.JBUI
import com.sun.net.httpserver.HttpExchange
import com.sun.net.httpserver.HttpHandler
import com.sun.net.httpserver.HttpServer
import kotlinx.coroutines.*
import okhttp3.*
import java.awt.BorderLayout
import java.awt.Dimension
import java.io.IOException
import java.net.InetSocketAddress
import java.net.ServerSocket
import java.net.URI
import java.util.concurrent.Executors
import javax.swing.*

/**
 * 全自动登录对话框 - 通过代理服务器自动捕获Cookie
 */
class AutoLoginDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {

    private val logger = thisLogger()
    private var isLoginDetected = false
    private var monitoringJob: Job? = null
    private var proxyServer: HttpServer? = null
    private val statusLabel = JBLabel("正在启动自动化登录检测...")
    private val proxyPort = findAvailablePort()
    private val capturedCookies = mutableMapOf<String, String>()
    private val client = OkHttpClient()
    
    init {
        title = "禅道登录 - 全自动检测"
        init()
        setSize(600, 350)
        setResizable(true)
        
        // 启动代理服务器和监控
        startProxyServer()
        startLoginMonitoring()
    }

    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())

        // 创建说明面板
        val instructionPanel = JPanel()
        instructionPanel.layout = BoxLayout(instructionPanel, BoxLayout.Y_AXIS)
        instructionPanel.border = JBUI.Borders.empty(20)

        val titleLabel = JBLabel("<html><h2>🚀 全自动登录检测</h2></html>")
        titleLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(titleLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        statusLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(statusLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        val stepsLabel = JBLabel("<html>" +
                "<h3>🎯 操作说明：</h3>" +
                "<ol style='line-height: 1.8;'>" +
                "<li><b>点击下方按钮</b>：在浏览器中打开禅道登录页面</li>" +
                "<li><b>正常登录</b>：在浏览器中输入用户名密码完成登录</li>" +
                "<li><b>自动检测</b>：插件会自动检测登录状态并获取认证信息</li>" +
                "<li><b>完成</b>：检测成功后自动开始数据爬取</li>" +
                "</ol>" +
                "<p><b>技术原理：</b>插件通过本地代理服务器自动捕获浏览器的HTTP请求和Cookie。</p>" +
                "</html>")
        instructionPanel.add(stepsLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        // 添加状态指示器
        val progressBar = JProgressBar()
        progressBar.isIndeterminate = true
        progressBar.string = "等待用户操作..."
        progressBar.isStringPainted = true
        instructionPanel.add(progressBar)

        panel.add(instructionPanel, BorderLayout.CENTER)

        return panel
    }

    /**
     * 查找可用端口
     */
    private fun findAvailablePort(): Int {
        return try {
            ServerSocket(0).use { socket ->
                socket.localPort
            }
        } catch (e: IOException) {
            8080 + (Math.random() * 1000).toInt()
        }
    }

    /**
     * 启动代理服务器
     */
    private fun startProxyServer() {
        try {
            proxyServer = HttpServer.create(InetSocketAddress("localhost", proxyPort), 0)
            proxyServer?.createContext("/", ProxyHandler())
            proxyServer?.executor = Executors.newFixedThreadPool(4)
            proxyServer?.start()
            
            logger.info("Proxy server started on port $proxyPort")
            statusLabel.text = "代理服务器已启动，端口: $proxyPort"
            
        } catch (e: Exception) {
            logger.error("Failed to start proxy server", e)
            statusLabel.text = "代理服务器启动失败: ${e.message}"
        }
    }

    /**
     * 代理处理器 - 捕获所有HTTP请求
     */
    private inner class ProxyHandler : HttpHandler {
        override fun handle(exchange: HttpExchange) {
            try {
                val method = exchange.requestMethod
                val uri = exchange.requestURI
                val headers = exchange.requestHeaders
                
                logger.info("Proxy request: $method $uri")
                
                // 检查是否是禅道相关请求
                if (uri.toString().contains("chandao.sw") || uri.toString().contains("zentao")) {
                    // 提取Cookie
                    val cookieHeader = headers.getFirst("Cookie")
                    if (!cookieHeader.isNullOrEmpty()) {
                        parseCookieHeader(cookieHeader)
                    }
                    
                    // 转发请求到实际的禅道服务器
                    forwardRequest(exchange, method, uri, headers)
                } else {
                    // 返回简单响应
                    val response = "Proxy is running"
                    exchange.sendResponseHeaders(200, response.length.toLong())
                    exchange.responseBody.write(response.toByteArray())
                    exchange.responseBody.close()
                }
                
            } catch (e: Exception) {
                logger.error("Error in proxy handler", e)
                try {
                    exchange.sendResponseHeaders(500, 0)
                    exchange.responseBody.close()
                } catch (ex: Exception) {
                    logger.error("Error sending error response", ex)
                }
            }
        }
    }

    /**
     * 解析Cookie头
     */
    private fun parseCookieHeader(cookieHeader: String) {
        try {
            val cookies = cookieHeader.split(";")
            for (cookie in cookies) {
                val parts = cookie.trim().split("=", limit = 2)
                if (parts.size == 2) {
                    val name = parts[0].trim()
                    val value = parts[1].trim()
                    capturedCookies[name] = value
                    logger.info("Captured cookie: $name = ${value.take(10)}...")
                }
            }
            
            // 检查是否获取到重要的认证Cookie
            if (capturedCookies.containsKey("PHPSESSID") || capturedCookies.containsKey("zentaosid")) {
                SwingUtilities.invokeLater {
                    handleLoginSuccess(capturedCookies.toMap())
                }
            }
            
        } catch (e: Exception) {
            logger.error("Error parsing cookie header", e)
        }
    }

    /**
     * 转发请求到实际服务器
     */
    private fun forwardRequest(exchange: HttpExchange, method: String, uri: URI, headers: com.sun.net.httpserver.Headers) {
        try {
            // 构建实际的禅道URL
            val targetUrl = "https://chandao.sw${uri.path}${if (uri.query != null) "?${uri.query}" else ""}"
            
            val requestBuilder = Request.Builder().url(targetUrl)
            
            // 复制请求头
            for ((key, values) in headers) {
                if (key.lowercase() != "host") {
                    for (value in values) {
                        requestBuilder.addHeader(key, value)
                    }
                }
            }
            
            val response = client.newCall(requestBuilder.build()).execute()
            val responseBody = response.body?.string() ?: ""
            
            // 返回响应
            exchange.responseHeaders.set("Content-Type", "text/html; charset=UTF-8")
            exchange.sendResponseHeaders(response.code, responseBody.length.toLong())
            exchange.responseBody.write(responseBody.toByteArray())
            exchange.responseBody.close()
            
        } catch (e: Exception) {
            logger.error("Error forwarding request", e)
            val errorResponse = "Error forwarding request: ${e.message}"
            exchange.sendResponseHeaders(500, errorResponse.length.toLong())
            exchange.responseBody.write(errorResponse.toByteArray())
            exchange.responseBody.close()
        }
    }

    /**
     * 启动登录监控
     */
    private fun startLoginMonitoring() {
        monitoringJob = CoroutineScope(Dispatchers.IO).launch {
            var attempts = 0
            val maxAttempts = 60 // 最多等待2分钟
            
            while (!isLoginDetected && isActive && attempts < maxAttempts) {
                try {
                    delay(2000) // 每2秒检查一次
                    attempts++
                    
                    SwingUtilities.invokeLater {
                        statusLabel.text = "正在监控登录状态... (${attempts}/$maxAttempts)"
                    }
                    
                    // 检查是否已经捕获到Cookie
                    if (capturedCookies.isNotEmpty()) {
                        SwingUtilities.invokeLater {
                            handleLoginSuccess(capturedCookies.toMap())
                        }
                        break
                    }
                    
                } catch (e: Exception) {
                    logger.error("Error in login monitoring", e)
                }
            }
            
            if (attempts >= maxAttempts && !isLoginDetected) {
                SwingUtilities.invokeLater {
                    statusLabel.text = "登录监控超时，请重试"
                }
            }
        }
    }

    /**
     * 打开浏览器进行登录
     */
    private fun openBrowserForLogin() {
        try {
            // 构建带代理的URL
            val proxyUrl = "http://localhost:$proxyPort/redirect?target=${java.net.URLEncoder.encode(initialUrl, "UTF-8")}"
            
            logger.info("Opening browser with proxy: $proxyUrl")
            BrowserUtil.browse(initialUrl) // 直接打开禅道页面，让用户正常登录
            
            statusLabel.text = "浏览器已打开，请完成登录。插件正在自动监控..."
            
        } catch (e: Exception) {
            logger.error("Failed to open browser", e)
            statusLabel.text = "打开浏览器失败: ${e.message}"
        }
    }

    /**
     * 处理登录成功
     */
    private fun handleLoginSuccess(cookies: Map<String, String>) {
        if (isLoginDetected) return
        isLoginDetected = true

        monitoringJob?.cancel()

        logger.info("Login success detected with ${cookies.size} cookies")
        
        SwingUtilities.invokeLater {
            statusLabel.text = "登录成功！正在传递认证信息..."
            onLoginSuccess(cookies)
            close(OK_EXIT_CODE)
        }
    }

    override fun createActions(): Array<Action> {
        val openBrowserAction = object : AbstractAction("打开禅道登录页面") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                openBrowserForLogin()
            }
        }

        return arrayOf(openBrowserAction, cancelAction)
    }
    
    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }
    
    override fun getPreferredSize(): Dimension {
        return Dimension(600, 350)
    }
    
    override fun dispose() {
        try {
            monitoringJob?.cancel()
            proxyServer?.stop(0)
            logger.info("Resources cleaned up successfully")
        } catch (e: Exception) {
            logger.warn("Error disposing resources", e)
        }
        super.dispose()
    }
}
