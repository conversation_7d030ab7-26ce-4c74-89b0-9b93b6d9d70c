package qhyu.asia.swcares.ui

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.JBUI
import com.sun.net.httpserver.HttpExchange
import com.sun.net.httpserver.HttpHandler
import com.sun.net.httpserver.HttpServer
import kotlinx.coroutines.*
import java.awt.BorderLayout
import java.awt.Dimension
import java.io.IOException
import java.net.InetSocketAddress
import java.net.ServerSocket
import java.util.concurrent.Executors
import javax.swing.*

/**
 * 简单可靠的登录对话框 - 基于本地服务器和系统浏览器
 */
class SimpleLoginDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {

    private val logger = thisLogger()
    private var isLoginDetected = false
    private var monitoringJob: Job? = null
    private var httpServer: HttpServer? = null
    private val statusLabel = JBLabel("准备启动登录流程...")
    private val serverPort = findAvailablePort()
    private val capturedCookies = mutableMapOf<String, String>()
    
    init {
        title = "禅道登录 - 简单可靠"
        init()
        setSize(700, 400)
        setResizable(true)
        
        // 启动本地服务器
        startLocalServer()
    }

    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())

        // 创建说明面板
        val instructionPanel = JPanel()
        instructionPanel.layout = BoxLayout(instructionPanel, BoxLayout.Y_AXIS)
        instructionPanel.border = JBUI.Borders.empty(20)

        val titleLabel = JBLabel("<html><h2>🚀 禅道登录 - 简单可靠</h2></html>")
        titleLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(titleLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        statusLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(statusLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        val stepsLabel = JBLabel("<html>" +
                "<h3>🎯 操作说明：</h3>" +
                "<ol style='line-height: 1.8;'>" +
                "<li><b>点击下方按钮</b>：在系统浏览器中打开禅道登录页面</li>" +
                "<li><b>正常登录</b>：在浏览器中输入用户名密码完成登录</li>" +
                "<li><b>访问特殊页面</b>：登录成功后，访问插件提供的特殊页面来传递Cookie</li>" +
                "<li><b>自动完成</b>：插件自动获取Cookie并开始数据爬取</li>" +
                "</ol>" +
                "<p><b>技术原理：</b>插件启动本地HTTP服务器，通过特殊页面自动获取浏览器Cookie。</p>" +
                "</html>")
        instructionPanel.add(stepsLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        // 添加状态指示器
        val progressBar = JProgressBar()
        progressBar.isIndeterminate = true
        progressBar.string = "等待用户操作..."
        progressBar.isStringPainted = true
        instructionPanel.add(progressBar)

        panel.add(instructionPanel, BorderLayout.CENTER)

        return panel
    }

    /**
     * 查找可用端口
     */
    private fun findAvailablePort(): Int {
        return try {
            ServerSocket(0).use { socket ->
                socket.localPort
            }
        } catch (e: IOException) {
            8080 + (Math.random() * 1000).toInt()
        }
    }

    /**
     * 启动本地服务器
     */
    private fun startLocalServer() {
        try {
            httpServer = HttpServer.create(InetSocketAddress("localhost", serverPort), 0)
            httpServer?.createContext("/", MainHandler())
            httpServer?.createContext("/login-success", LoginSuccessHandler())
            httpServer?.executor = Executors.newFixedThreadPool(2)
            httpServer?.start()
            
            logger.info("Local server started on port $serverPort")
            statusLabel.text = "本地服务器已启动，端口: $serverPort"
            
        } catch (e: Exception) {
            logger.error("Failed to start local server", e)
            statusLabel.text = "服务器启动失败: ${e.message}"
        }
    }

    /**
     * 主页面处理器
     */
    private inner class MainHandler : HttpHandler {
        override fun handle(exchange: HttpExchange) {
            try {
                val response = createMainPage()
                
                exchange.responseHeaders.set("Content-Type", "text/html; charset=UTF-8")
                exchange.sendResponseHeaders(200, response.toByteArray().size.toLong())
                exchange.responseBody.write(response.toByteArray())
                exchange.responseBody.close()
                
            } catch (e: Exception) {
                logger.error("Error in main handler", e)
            }
        }
    }

    /**
     * 登录成功处理器
     */
    private inner class LoginSuccessHandler : HttpHandler {
        override fun handle(exchange: HttpExchange) {
            try {
                val method = exchange.requestMethod
                
                if (method == "POST") {
                    // 读取POST数据
                    val requestBody = exchange.requestBody.readBytes().toString(Charsets.UTF_8)
                    logger.info("Received login data: $requestBody")
                    
                    // 解析Cookie数据
                    val cookies = parseCookieData(requestBody)
                    
                    if (cookies.isNotEmpty()) {
                        SwingUtilities.invokeLater {
                            handleLoginSuccess(cookies)
                        }
                    }
                }
                
                val response = """
                    <html>
                    <head><title>登录成功</title></head>
                    <body>
                        <h2>✅ 登录信息已成功传递给插件！</h2>
                        <p>您可以关闭此页面，返回IDEA查看数据爬取结果。</p>
                        <script>
                            setTimeout(() => {
                                window.close();
                            }, 3000);
                        </script>
                    </body>
                    </html>
                """.trimIndent()
                
                exchange.responseHeaders.set("Content-Type", "text/html; charset=UTF-8")
                exchange.sendResponseHeaders(200, response.toByteArray().size.toLong())
                exchange.responseBody.write(response.toByteArray())
                exchange.responseBody.close()
                
            } catch (e: Exception) {
                logger.error("Error in login success handler", e)
            }
        }
    }

    /**
     * 创建主页面HTML
     */
    private fun createMainPage(): String {
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>禅道登录助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .button {
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="text-align: center;">
            <h1>🚀 禅道登录助手</h1>
            <p>IDEA插件自动登录页面</p>
        </div>

        <div style="margin: 30px 0;">
            <h3>📋 操作步骤：</h3>
            <ol style="line-height: 1.8;">
                <li>点击下方按钮在新窗口中打开禅道登录页面</li>
                <li>在新窗口中完成禅道登录</li>
                <li>登录成功后返回此页面，点击"获取登录信息"按钮</li>
                <li>系统会自动传递登录信息给IDEA插件</li>
            </ol>
        </div>

        <div style="text-align: center;">
            <button class="button" onclick="openChandaoLogin()">🔗 打开禅道登录页面</button>
            <button class="button" onclick="getCookiesAndSend()">🍪 获取登录信息</button>
        </div>

        <div id="status" class="status">
            点击上方按钮开始登录流程
        </div>
    </div>

    <script>
        function openChandaoLogin() {
            const status = document.getElementById('status');
            window.open('$initialUrl', 'chandaoLogin', 'width=1200,height=800,scrollbars=yes,resizable=yes');
            status.innerHTML = '🔄 已在新窗口中打开禅道登录页面，请完成登录后返回此页面';
        }

        function getCookiesAndSend() {
            const status = document.getElementById('status');
            status.innerHTML = '🔍 正在获取登录信息...';

            // 获取当前页面的Cookie
            const cookies = {};
            document.cookie.split(';').forEach(cookie => {
                const [name, value] = cookie.trim().split('=');
                if (name && value) cookies[name] = value;
            });

            // 发送Cookie到服务器
            fetch('/login-success', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(cookies)
            })
            .then(response => response.text())
            .then(data => {
                status.innerHTML = '✅ 登录信息已成功传递给插件！';
                setTimeout(() => {
                    window.close();
                }, 2000);
            })
            .catch(error => {
                status.innerHTML = '❌ 传递失败: ' + error.message;
            });
        }
    </script>
</body>
</html>
        """.trimIndent()
    }

    /**
     * 解析Cookie数据
     */
    private fun parseCookieData(data: String): Map<String, String> {
        val cookies = mutableMapOf<String, String>()
        
        try {
            // 简单的JSON解析
            if (data.trim().startsWith("{")) {
                val pattern = Regex("\"([^\"]+)\"\\s*:\\s*\"([^\"]+)\"")
                val matches = pattern.findAll(data)
                
                for (match in matches) {
                    val key = match.groupValues[1]
                    val value = match.groupValues[2]
                    cookies[key] = value
                }
            }
            
            logger.info("Parsed ${cookies.size} cookies from data")
            
        } catch (e: Exception) {
            logger.error("Error parsing cookie data", e)
        }
        
        return cookies
    }

    /**
     * 打开浏览器进行登录
     */
    private fun openBrowserForLogin() {
        try {
            val localUrl = "http://localhost:$serverPort/"
            logger.info("Opening browser with local URL: $localUrl")
            BrowserUtil.browse(localUrl)
            statusLabel.text = "浏览器已打开，请按照页面指引完成登录"
            
        } catch (e: Exception) {
            logger.error("Failed to open browser", e)
            statusLabel.text = "打开浏览器失败: ${e.message}"
        }
    }

    /**
     * 处理登录成功
     */
    private fun handleLoginSuccess(cookies: Map<String, String>) {
        if (isLoginDetected) return
        isLoginDetected = true

        monitoringJob?.cancel()

        logger.info("Login success detected with ${cookies.size} cookies")
        
        SwingUtilities.invokeLater {
            statusLabel.text = "登录成功！正在传递认证信息..."
            onLoginSuccess(cookies)
            close(OK_EXIT_CODE)
        }
    }

    override fun createActions(): Array<Action> {
        val openBrowserAction = object : AbstractAction("开始登录流程") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                openBrowserForLogin()
            }
        }

        return arrayOf(openBrowserAction, cancelAction)
    }
    
    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }
    
    override fun getPreferredSize(): Dimension {
        return Dimension(700, 400)
    }
    
    override fun dispose() {
        try {
            monitoringJob?.cancel()
            httpServer?.stop(0)
            logger.info("Resources cleaned up successfully")
        } catch (e: Exception) {
            logger.warn("Error disposing resources", e)
        }
        super.dispose()
    }
}
