package qhyu.asia.swcares.ui

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.JBUI
import com.sun.net.httpserver.HttpExchange
import com.sun.net.httpserver.HttpHandler
import com.sun.net.httpserver.HttpServer
import kotlinx.coroutines.*
import okhttp3.*
import java.awt.BorderLayout
import java.awt.Dimension
import java.io.IOException
import java.net.InetSocketAddress
import java.net.ServerSocket
import java.util.concurrent.Executors
import javax.swing.*

/**
 * 智能浏览器登录对话框 - 自动检测登录状态
 */
class JavaFXBrowserDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {

    private val logger = thisLogger()
    private var isLoginDetected = false
    private var monitoringJob: Job? = null
    private var httpServer: HttpServer? = null
    private val statusLabel = JBLabel("准备启动登录流程...")
    private val callbackPort = findAvailablePort()
    private val callbackUrl = "http://localhost:$callbackPort/callback"
    private val client = OkHttpClient.Builder()
        .cookieJar(CookieJar.NO_COOKIES)
        .build()

    init {
        title = "禅道登录 - 智能检测"
        init()
        setSize(600, 400)
        setResizable(true)

        // 启动本地服务器
        startCallbackServer()
    }

    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())

        // 创建说明面板
        val instructionPanel = JPanel()
        instructionPanel.layout = BoxLayout(instructionPanel, BoxLayout.Y_AXIS)
        instructionPanel.border = JBUI.Borders.empty(20)

        val titleLabel = JBLabel("<html><h2>🌐 智能登录检测</h2></html>")
        titleLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(titleLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        statusLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(statusLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        val stepsLabel = JBLabel("<html>" +
                "<h3>操作步骤：</h3>" +
                "<ol>" +
                "<li>点击下方打开禅道登录页面按钮</li>" +
                "<li>在打开的浏览器中完成禅道登录</li>" +
                "<li>登录成功后，插件会自动检测到登录状态</li>" +
                "<li>检测成功后会自动关闭此对话框并开始数据爬取</li>" +
                "</ol>" +
                "<p><b>技术原理：</b>插件会启动本地服务器监听登录状态，并定期检查禅道网站的认证状态。</p>" +
                "</html>")
        instructionPanel.add(stepsLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        // 添加状态指示器
        val progressBar = JProgressBar()
        progressBar.isIndeterminate = true
        progressBar.string = "等待用户操作..."
        progressBar.isStringPainted = true
        instructionPanel.add(progressBar)

        panel.add(instructionPanel, BorderLayout.CENTER)

        return panel
    }

    /**
     * 查找可用端口
     */
    private fun findAvailablePort(): Int {
        return try {
            ServerSocket(0).use { socket ->
                socket.localPort
            }
        } catch (e: IOException) {
            8080 + (Math.random() * 1000).toInt()
        }
    }

    /**
     * 启动本地回调服务器
     */
    private fun startCallbackServer() {
        try {
            httpServer = HttpServer.create(InetSocketAddress(callbackPort), 0)
            httpServer?.createContext("/callback", CallbackHandler())
            httpServer?.createContext("/status", StatusHandler())
            httpServer?.executor = Executors.newFixedThreadPool(2)
            httpServer?.start()

            logger.info("Callback server started on port $callbackPort")
            statusLabel.text = "本地服务器已启动，端口: $callbackPort"

        } catch (e: Exception) {
            logger.error("Failed to start callback server", e)
            statusLabel.text = "服务器启动失败: ${e.message}"
        }
    }

    /**
     * 处理登录回调
     */
    private inner class CallbackHandler : HttpHandler {
        override fun handle(exchange: HttpExchange) {
            try {
                val method = exchange.requestMethod
                val uri = exchange.requestURI

                logger.info("Received callback: $method $uri")

                if (method == "GET") {
                    // 从查询参数中获取登录状态
                    val query = uri.query ?: ""
                    if (query.contains("success=true")) {
                        // 登录成功，开始检测Cookie
                        SwingUtilities.invokeLater {
                            statusLabel.text = "检测到登录成功信号，正在验证..."
                            startLoginDetection()
                        }
                    }
                }

                // 返回成功页面
                val response = """
                    <html>
                    <head><title>登录检测</title></head>
                    <body>
                        <h2>登录状态已接收</h2>
                        <p>插件正在验证登录状态，请稍候...</p>
                        <script>
                            setTimeout(() => {
                                window.close();
                            }, 3000);
                        </script>
                    </body>
                    </html>
                """.trimIndent()

                exchange.responseHeaders.set("Content-Type", "text/html; charset=UTF-8")
                exchange.sendResponseHeaders(200, response.toByteArray().size.toLong())
                exchange.responseBody.write(response.toByteArray())
                exchange.responseBody.close()

            } catch (e: Exception) {
                logger.error("Error handling callback", e)
            }
        }
    }

    /**
     * 处理状态查询
     */
    private inner class StatusHandler : HttpHandler {
        override fun handle(exchange: HttpExchange) {
            try {
                val response = """{"status": "waiting", "port": $callbackPort}"""
                exchange.responseHeaders.set("Content-Type", "application/json")
                exchange.sendResponseHeaders(200, response.toByteArray().size.toLong())
                exchange.responseBody.write(response.toByteArray())
                exchange.responseBody.close()
            } catch (e: Exception) {
                logger.error("Error handling status request", e)
            }
        }
    }

    /**
     * 开始登录检测
     */
    private fun startLoginDetection() {
        if (monitoringJob?.isActive == true) return

        monitoringJob = CoroutineScope(Dispatchers.IO).launch {
            var attempts = 0
            val maxAttempts = 30 // 最多尝试30次，每次间隔2秒

            while (!isLoginDetected && isActive && attempts < maxAttempts) {
                try {
                    delay(2000) // 每2秒检查一次
                    attempts++

                    SwingUtilities.invokeLater {
                        statusLabel.text = "正在检测登录状态... (${attempts}/$maxAttempts)"
                    }

                    // 检查禅道网站的登录状态
                    val isLoggedIn = checkChandaoLoginStatus()

                    if (isLoggedIn) {
                        // 登录成功，提取Cookie
                        val cookies = extractCookiesFromBrowser()

                        SwingUtilities.invokeLater {
                            handleLoginSuccess(cookies)
                        }
                        break
                    }

                } catch (e: Exception) {
                    logger.error("Error in login detection", e)
                }
            }

            if (attempts >= maxAttempts && !isLoginDetected) {
                SwingUtilities.invokeLater {
                    statusLabel.text = "登录检测超时，请手动确认登录状态"
                }
            }
        }
    }

    /**
     * 检查禅道网站的登录状态
     */
    private suspend fun checkChandaoLoginStatus(): Boolean = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url("${initialUrl.substringBefore("/user-login")}/my/")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .build()

            val response = client.newCall(request).execute()
            val body = response.body?.string() ?: ""

            // 如果没有被重定向到登录页面，说明已登录
            val isLoggedIn = response.isSuccessful && !body.contains("user-login") && !body.contains("登录")

            logger.info("Login status check: $isLoggedIn, response code: ${response.code}")

            isLoggedIn
        } catch (e: Exception) {
            logger.error("Error checking login status", e)
            false
        }
    }

    /**
     * 从浏览器提取Cookie（模拟方式）
     */
    private suspend fun extractCookiesFromBrowser(): Map<String, String> = withContext(Dispatchers.IO) {
        val cookies = mutableMapOf<String, String>()

        try {
            // 尝试访问禅道页面并获取Set-Cookie头
            val request = Request.Builder()
                .url("${initialUrl.substringBefore("/user-login")}/my/")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .build()

            val response = client.newCall(request).execute()

            // 从响应头中提取Cookie
            response.headers.values("Set-Cookie").forEach { cookieHeader ->
                val cookieParts = cookieHeader.split(";")[0].split("=", limit = 2)
                if (cookieParts.size == 2) {
                    cookies[cookieParts[0].trim()] = cookieParts[1].trim()
                }
            }

            logger.info("Extracted ${cookies.size} cookies from response headers")

            // 如果没有获取到Cookie，创建一个确认标记
            if (cookies.isEmpty()) {
                cookies["user_confirmed_login"] = "true"
                logger.info("No cookies found, using confirmation marker")
            }

        } catch (e: Exception) {
            logger.error("Error extracting cookies", e)
            cookies["user_confirmed_login"] = "true"
        }

        cookies
    }

    /**
     * 打开浏览器进行登录
     */
    private fun openBrowserForLogin() {
        try {
            logger.info("Opening browser for login: $initialUrl")
            BrowserUtil.browse(initialUrl)
            statusLabel.text = "浏览器已打开，请完成登录..."

            // 开始登录检测
            startLoginDetection()

        } catch (e: Exception) {
            logger.error("Failed to open browser", e)
            statusLabel.text = "打开浏览器失败: ${e.message}"
        }
    }

    /**
     * 处理登录成功
     */
    private fun handleLoginSuccess(cookies: Map<String, String>) {
        if (isLoginDetected) return
        isLoginDetected = true

        monitoringJob?.cancel()

        logger.info("Login success detected with ${cookies.size} cookies")

        SwingUtilities.invokeLater {
            statusLabel.text = "登录成功！正在传递认证信息..."
            onLoginSuccess(cookies)
            close(OK_EXIT_CODE)
        }
    }

    override fun createActions(): Array<Action> {
        val openBrowserAction = object : AbstractAction("打开禅道登录页面") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                openBrowserForLogin()
            }
        }

        val manualConfirmAction = object : AbstractAction("手动确认登录") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                // 手动确认登录，直接开始检测
                statusLabel.text = "开始手动检测登录状态..."
                startLoginDetection()
            }
        }

        return arrayOf(openBrowserAction, manualConfirmAction, cancelAction)
    }

    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }

    override fun getPreferredSize(): Dimension {
        return Dimension(600, 400)
    }

    override fun dispose() {
        try {
            monitoringJob?.cancel()
            httpServer?.stop(0)
            logger.info("Resources cleaned up successfully")
        } catch (e: Exception) {
            logger.warn("Error disposing resources", e)
        }
        super.dispose()
    }
}
