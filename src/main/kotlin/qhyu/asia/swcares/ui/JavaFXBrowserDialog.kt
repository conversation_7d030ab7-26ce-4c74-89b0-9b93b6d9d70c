package qhyu.asia.swcares.ui

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBTextArea
import com.intellij.util.ui.JBUI
import kotlinx.coroutines.*
import java.awt.BorderLayout
import java.awt.Dimension
import java.awt.Font
import java.awt.event.ActionEvent
import java.io.File
import javax.swing.*

/**
 * 实用的浏览器登录对话框 - 基于书签的Cookie获取
 */
class JavaFXBrowserDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {

    private val logger = thisLogger()
    private var isLoginDetected = false
    private var monitoringJob: Job? = null
    private val statusLabel = JBLabel("请按照下方步骤操作")
    private val cookieTextArea = JBTextArea(6, 60)

    init {
        title = "禅道登录 - 实用方案"
        init()
        setSize(800, 500)
        setResizable(true)

        // 开始监控文件
        startFileMonitoring()
    }

    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())

        // 创建说明面板
        val instructionPanel = JPanel()
        instructionPanel.layout = BoxLayout(instructionPanel, BoxLayout.Y_AXIS)
        instructionPanel.border = JBUI.Borders.empty(15)

        val titleLabel = JBLabel("<html><h2>🌐 禅道登录 - 实用方案</h2></html>")
        titleLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(titleLabel)

        instructionPanel.add(Box.createVerticalStrut(15))

        statusLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(statusLabel)

        instructionPanel.add(Box.createVerticalStrut(15))

        val stepsLabel = JBLabel("<html>" +
                "<h3>📋 简单三步操作：</h3>" +
                "<ol style='line-height: 1.6;'>" +
                "<li><b>打开登录页面</b>：点击下方按钮在浏览器中登录禅道</li>" +
                "<li><b>添加书签</b>：登录成功后，将下方的JavaScript代码添加为浏览器书签</li>" +
                "<li><b>点击书签</b>：在禅道页面点击书签，Cookie会自动传递给插件</li>" +
                "</ol>" +
                "</html>")
        instructionPanel.add(stepsLabel)

        instructionPanel.add(Box.createVerticalStrut(15))

        // JavaScript 书签代码
        val bookmarkLabel = JBLabel("📌 请将以下代码添加为浏览器书签：")
        instructionPanel.add(bookmarkLabel)

        val bookmarkCode = createBookmarkCode()
        val codeArea = JBTextArea(3, 60)
        codeArea.text = bookmarkCode
        codeArea.isEditable = false
        codeArea.font = Font(Font.MONOSPACED, Font.PLAIN, 11)
        codeArea.background = panel.background
        codeArea.lineWrap = true
        codeArea.wrapStyleWord = true
        val codeScrollPane = JScrollPane(codeArea)
        codeScrollPane.preferredSize = Dimension(700, 80)
        instructionPanel.add(codeScrollPane)

        instructionPanel.add(Box.createVerticalStrut(10))

        // Cookie 输入区域
        val cookieLabel = JBLabel("🍪 或者手动粘贴Cookie（格式：name1=value1; name2=value2）：")
        instructionPanel.add(cookieLabel)

        cookieTextArea.text = "例如：PHPSESSID=abc123; zentaosid=def456"
        cookieTextArea.font = Font(Font.MONOSPACED, Font.PLAIN, 12)
        val cookieScrollPane = JScrollPane(cookieTextArea)
        cookieScrollPane.preferredSize = Dimension(700, 100)
        instructionPanel.add(cookieScrollPane)

        panel.add(instructionPanel, BorderLayout.CENTER)

        return panel
    }

    /**
     * 创建书签代码
     */
    private fun createBookmarkCode(): String {
        val tempDir = System.getProperty("java.io.tmpdir")
        return """javascript:(function(){
var cookies = {};
document.cookie.split(';').forEach(function(cookie) {
    var parts = cookie.trim().split('=');
    if (parts.length === 2) cookies[parts[0]] = parts[1];
});
var data = JSON.stringify(cookies, null, 2);
var xhr = new XMLHttpRequest();
xhr.open('POST', 'data:text/plain;charset=utf-8,' + encodeURIComponent(data));
var blob = new Blob([data], {type: 'application/json'});
var url = URL.createObjectURL(blob);
var a = document.createElement('a');
a.href = url;
a.download = 'chandao_cookies.json';
a.style.display = 'none';
document.body.appendChild(a);
a.click();
document.body.removeChild(a);
URL.revokeObjectURL(url);
alert('Cookie已保存到下载文件夹！请返回IDEA插件点击"检测Cookie文件"按钮。');
})();"""
    }

    /**
     * 开始文件监控
     */
    private fun startFileMonitoring() {
        monitoringJob = CoroutineScope(Dispatchers.IO).launch {
            while (!isLoginDetected && isActive) {
                try {
                    delay(2000) // 每2秒检查一次
                    checkForCookieFiles()
                } catch (e: Exception) {
                    logger.error("Error in file monitoring", e)
                }
            }
        }
    }

    /**
     * 检查Cookie文件
     */
    private fun checkForCookieFiles() {
        try {
            // 检查下载文件夹
            val userHome = System.getProperty("user.home")
            val downloadDir = File(userHome, "Downloads")
            val cookieFile = File(downloadDir, "chandao_cookies.json")

            if (cookieFile.exists() && cookieFile.lastModified() > System.currentTimeMillis() - 60000) {
                val cookieData = cookieFile.readText()
                logger.info("Found cookie file: ${cookieFile.absolutePath}")

                val cookies = parseCookieFile(cookieData)
                if (cookies.isNotEmpty()) {
                    SwingUtilities.invokeLater {
                        handleLoginSuccess(cookies)
                    }
                    cookieFile.delete() // 删除文件
                }
            }
        } catch (e: Exception) {
            logger.error("Error checking cookie files", e)
        }
    }

    /**
     * 解析Cookie文件
     */
    private fun parseCookieFile(cookieData: String): Map<String, String> {
        val cookies = mutableMapOf<String, String>()

        try {
            // 尝试解析JSON格式
            if (cookieData.trim().startsWith("{")) {
                val pattern = Regex("\"([^\"]+)\"\\s*:\\s*\"([^\"]+)\"")
                val matches = pattern.findAll(cookieData)

                for (match in matches) {
                    val key = match.groupValues[1]
                    val value = match.groupValues[2]
                    cookies[key] = value
                }
            }

            logger.info("Parsed ${cookies.size} cookies from file")

        } catch (e: Exception) {
            logger.error("Error parsing cookie file", e)
        }

        return cookies
    }

    /**
     * 解析手动输入的Cookie
     */
    private fun parseManualCookies(cookieText: String): Map<String, String> {
        val cookies = mutableMapOf<String, String>()

        try {
            // 支持多种格式：name1=value1; name2=value2 或 name1=value1\nname2=value2
            val cookieLines = cookieText.split(";", "\n")

            for (line in cookieLines) {
                val parts = line.trim().split("=", limit = 2)
                if (parts.size == 2) {
                    val name = parts[0].trim()
                    val value = parts[1].trim()
                    if (name.isNotEmpty() && value.isNotEmpty()) {
                        cookies[name] = value
                    }
                }
            }

            logger.info("Parsed ${cookies.size} cookies from manual input")

        } catch (e: Exception) {
            logger.error("Error parsing manual cookies", e)
        }

        return cookies
    }

    /**
     * 打开浏览器进行登录
     */
    private fun openBrowserForLogin() {
        try {
            logger.info("Opening browser for login: $initialUrl")
            BrowserUtil.browse(initialUrl)
            statusLabel.text = "浏览器已打开，请完成登录并使用上方的书签或手动输入Cookie"
        } catch (e: Exception) {
            logger.error("Failed to open browser", e)
            statusLabel.text = "打开浏览器失败: ${e.message}"
        }
    }

    /**
     * 检测Cookie文件
     */
    private fun detectCookieFile() {
        try {
            checkForCookieFiles()
            if (!isLoginDetected) {
                statusLabel.text = "未找到Cookie文件，请确保已点击书签或手动输入Cookie"
            }
        } catch (e: Exception) {
            logger.error("Error detecting cookie file", e)
            statusLabel.text = "检测失败: ${e.message}"
        }
    }

    /**
     * 处理手动输入的Cookie
     */
    private fun processManualCookies() {
        val cookieText = cookieTextArea.text.trim()

        if (cookieText.isEmpty()) {
            JOptionPane.showMessageDialog(
                this.contentPane,
                "请先输入Cookie数据！",
                "输入错误",
                JOptionPane.WARNING_MESSAGE
            )
            return
        }

        val cookies = parseManualCookies(cookieText)

        if (cookies.isEmpty()) {
            JOptionPane.showMessageDialog(
                this.contentPane,
                "未能解析出有效的Cookie数据，请检查格式！",
                "解析错误",
                JOptionPane.ERROR_MESSAGE
            )
            return
        }

        logger.info("Successfully parsed ${cookies.size} cookies from manual input")
        handleLoginSuccess(cookies)
    }

    /**
     * 处理登录成功
     */
    private fun handleLoginSuccess(cookies: Map<String, String>) {
        if (isLoginDetected) return
        isLoginDetected = true

        monitoringJob?.cancel()

        logger.info("Login success detected with ${cookies.size} cookies")

        SwingUtilities.invokeLater {
            statusLabel.text = "登录成功！正在传递认证信息..."
            onLoginSuccess(cookies)
            close(OK_EXIT_CODE)
        }
    }

    override fun createActions(): Array<Action> {
        val openBrowserAction = object : AbstractAction("打开禅道登录页面") {
            override fun actionPerformed(e: ActionEvent?) {
                openBrowserForLogin()
            }
        }

        val detectFileAction = object : AbstractAction("检测Cookie文件") {
            override fun actionPerformed(e: ActionEvent?) {
                detectCookieFile()
            }
        }

        val manualInputAction = object : AbstractAction("使用手动输入的Cookie") {
            override fun actionPerformed(e: ActionEvent?) {
                processManualCookies()
            }
        }

        return arrayOf(openBrowserAction, detectFileAction, manualInputAction, cancelAction)
    }



    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }

    override fun getPreferredSize(): Dimension {
        return Dimension(800, 500)
    }

    override fun dispose() {
        try {
            monitoringJob?.cancel()
            logger.info("Resources cleaned up successfully")
        } catch (e: Exception) {
            logger.warn("Error disposing resources", e)
        }
        super.dispose()
    }
}
