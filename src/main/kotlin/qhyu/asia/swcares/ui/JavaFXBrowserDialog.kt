package qhyu.asia.swcares.ui

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBTextArea
import com.intellij.util.ui.JBUI
import kotlinx.coroutines.*
import java.awt.BorderLayout
import java.awt.Dimension
import java.awt.Font
import java.awt.event.ActionEvent
import javax.swing.*

/**
 * 简化的浏览器登录对话框 - 使用系统浏览器和手动 Cookie 输入
 */
class JavaFXBrowserDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {

    private val logger = thisLogger()
    private var isLoginDetected = false
    private var monitoringJob: Job? = null
    private val statusLabel = JBLabel("点击下方按钮开始登录流程")
    private val cookieTextArea = JBTextArea(8, 50)

    init {
        title = "禅道登录 - 系统浏览器"
        init()
        setSize(800, 600)
        setResizable(true)
    }

    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())

        // 创建顶部状态面板
        val topPanel = JPanel(BorderLayout())
        topPanel.border = JBUI.Borders.empty(10)

        val titleLabel = JBLabel("<html><h3>🌐 禅道登录 - 系统浏览器</h3></html>")
        topPanel.add(titleLabel, BorderLayout.WEST)

        topPanel.add(statusLabel, BorderLayout.CENTER)

        panel.add(topPanel, BorderLayout.NORTH)

        // 创建主要内容面板
        val mainPanel = JPanel()
        mainPanel.layout = BoxLayout(mainPanel, BoxLayout.Y_AXIS)
        mainPanel.border = JBUI.Borders.empty(20)

        // 说明文字
        val instructionLabel = JBLabel("<html>" +
                "<h4>操作步骤：</h4>" +
                "<ol>" +
                "<li>点击下方"打开禅道登录页面"按钮</li>" +
                "<li>在打开的浏览器中完成禅道登录</li>" +
                "<li>登录成功后，按 F12 打开开发者工具</li>" +
                "<li>在 Console 中运行下面的脚本获取 Cookie</li>" +
                "<li>将获取的 Cookie 粘贴到下方文本框中</li>" +
                "<li>点击"确认登录"完成流程</li>" +
                "</ol>" +
                "</html>")
        mainPanel.add(instructionLabel)

        mainPanel.add(Box.createVerticalStrut(20))

        // Cookie 脚本
        val scriptLabel = JBLabel("在浏览器控制台中运行以下脚本：")
        mainPanel.add(scriptLabel)

        val scriptArea = JBTextArea(4, 50)
        scriptArea.text = """
// 禅道 Cookie 提取脚本
const cookies = {};
document.cookie.split(';').forEach(cookie => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) cookies[name] = value;
});
console.log('提取的Cookie:', JSON.stringify(cookies, null, 2));
copy(JSON.stringify(cookies));
alert('Cookie已复制到剪贴板！请粘贴到IDEA插件中');
        """.trimIndent()
        scriptArea.isEditable = false
        scriptArea.font = Font(Font.MONOSPACED, Font.PLAIN, 12)
        scriptArea.background = panel.background
        val scriptScrollPane = JScrollPane(scriptArea)
        scriptScrollPane.preferredSize = Dimension(600, 100)
        mainPanel.add(scriptScrollPane)

        mainPanel.add(Box.createVerticalStrut(20))

        // Cookie 输入区域
        val cookieLabel = JBLabel("请将获取的 Cookie JSON 粘贴到下方：")
        mainPanel.add(cookieLabel)

        cookieTextArea.placeholder = "请粘贴从浏览器控制台获取的 Cookie JSON 数据..."
        cookieTextArea.font = Font(Font.MONOSPACED, Font.PLAIN, 12)
        val cookieScrollPane = JScrollPane(cookieTextArea)
        cookieScrollPane.preferredSize = Dimension(600, 150)
        mainPanel.add(cookieScrollPane)

        panel.add(mainPanel, BorderLayout.CENTER)

        return panel
    }

    private fun openBrowserForLogin() {
        try {
            logger.info("Opening browser for login: $initialUrl")
            BrowserUtil.browse(initialUrl)
            statusLabel.text = "浏览器已打开，请完成登录并获取 Cookie"
        } catch (e: Exception) {
            logger.error("Failed to open browser", e)
            statusLabel.text = "打开浏览器失败: ${e.message}"
        }
    }

    private fun processCookieInput(): Boolean {
        val cookieText = cookieTextArea.text.trim()

        if (cookieText.isEmpty()) {
            JOptionPane.showMessageDialog(
                this.contentPane,
                "请先粘贴 Cookie 数据！",
                "输入错误",
                JOptionPane.WARNING_MESSAGE
            )
            return false
        }

        try {
            // 尝试解析 JSON
            val cookies = parseCookieJson(cookieText)

            if (cookies.isEmpty()) {
                JOptionPane.showMessageDialog(
                    this.contentPane,
                    "未能从输入中解析出有效的 Cookie 数据！",
                    "解析错误",
                    JOptionPane.ERROR_MESSAGE
                )
                return false
            }

            logger.info("Successfully parsed ${cookies.size} cookies")
            handleLoginSuccess(cookies)
            return true

        } catch (e: Exception) {
            logger.error("Error parsing cookie input", e)
            JOptionPane.showMessageDialog(
                this.contentPane,
                "Cookie 数据格式错误，请确保粘贴的是有效的 JSON 格式！\n错误: ${e.message}",
                "格式错误",
                JOptionPane.ERROR_MESSAGE
            )
            return false
        }
    }

    private fun parseCookieJson(cookieText: String): Map<String, String> {
        val cookies = mutableMapOf<String, String>()

        try {
            // 简单的 JSON 解析
            val cleanText = cookieText.trim().removePrefix("{").removeSuffix("}")

            // 使用正则表达式提取键值对
            val pattern = Regex("\"([^\"]+)\"\\s*:\\s*\"([^\"]+)\"")
            val matches = pattern.findAll(cleanText)

            for (match in matches) {
                val key = match.groupValues[1]
                val value = match.groupValues[2]
                cookies[key] = value
            }

            logger.info("Parsed cookies: ${cookies.keys}")

        } catch (e: Exception) {
            logger.error("Error parsing cookie JSON", e)
        }

        return cookies
    }

    private fun handleLoginSuccess(cookies: Map<String, String>) {
        if (isLoginDetected) return
        isLoginDetected = true

        monitoringJob?.cancel()

        logger.info("Login success detected with ${cookies.size} cookies")

        SwingUtilities.invokeLater {
            statusLabel.text = "登录成功！正在传递认证信息..."
            onLoginSuccess(cookies)
            close(OK_EXIT_CODE)
        }
    }

    override fun createActions(): Array<Action> {
        val openBrowserAction = object : AbstractAction("打开禅道登录页面") {
            override fun actionPerformed(e: ActionEvent?) {
                openBrowserForLogin()
            }
        }

        val confirmLoginAction = object : AbstractAction("确认登录") {
            override fun actionPerformed(e: ActionEvent?) {
                processCookieInput()
            }
        }

        return arrayOf(openBrowserAction, confirmLoginAction, cancelAction)
    }

    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }

    override fun getPreferredSize(): Dimension {
        return Dimension(800, 600)
    }

    override fun dispose() {
        try {
            monitoringJob?.cancel()
        } catch (e: Exception) {
            logger.warn("Error disposing resources", e)
        }
        super.dispose()
    }
}
