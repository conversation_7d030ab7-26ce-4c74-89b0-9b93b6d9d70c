package qhyu.asia.swcares.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.wm.ToolWindowManager
import kotlinx.coroutines.runBlocking
import qhyu.asia.swcares.service.ChandaoWebService
import qhyu.asia.swcares.ui.AutoLoginDialog
import javax.swing.SwingUtilities

/**
 * 打开禅道的主要操作
 */
class OpenChandaoAction : AnAction() {
    
    private val logger = thisLogger()
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val webService = project.service<ChandaoWebService>()
        
        logger.info("OpenChandaoAction triggered")
        
        // 在后台线程中检查登录状态
        Thread {
            runBlocking {
                try {
                    val isLoggedIn = webService.checkLoginStatus()
                    
                    SwingUtilities.invokeLater {
                        if (isLoggedIn) {
                            // 已登录（可能使用了保存的Cookie），直接爬取数据
                            Messages.showInfoMessage(
                                project,
                                "检测到有效的登录状态，正在获取数据...",
                                "自动登录成功"
                            )
                            crawlDataAndShowResults(project, webService)
                        } else {
                            // 未登录，显示登录对话框
                            showAutoLoginDialog(project, webService)
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error checking login status", e)
                    SwingUtilities.invokeLater {
                        Messages.showErrorDialog(
                            project,
                            "检查登录状态时发生错误: ${e.message}",
                            "错误"
                        )
                    }
                }
            }
        }.start()
    }
    
    private fun showAutoLoginDialog(project: Project, webService: ChandaoWebService) {
        val config = webService.getConfig()
        val loginUrl = "${config.baseUrl}${config.loginPath}"

        logger.info("Opening auto login dialog with URL: $loginUrl")

        try {
            val dialog = AutoLoginDialog(project, loginUrl) { cookies ->
                // 登录成功回调
                logger.info("Login successful via auto detection, received ${cookies.size} cookies")

                // 设置登录 Cookie
                webService.setLoginCookies(cookies)

                // 显示成功消息并开始爬取数据
                SwingUtilities.invokeLater {
                    Messages.showInfoMessage(project, "自动登录成功！正在爬取数据...", "成功")
                    crawlDataAndShowResults(project, webService)
                }
            }

            dialog.show()

        } catch (e: Exception) {
            logger.error("Failed to create auto login dialog", e)
            Messages.showErrorDialog(
                project,
                "自动登录对话框初始化失败: ${e.message}",
                "错误"
            )
        }
    }
    
    private fun crawlDataAndShowResults(project: Project, webService: ChandaoWebService) {
        runBlocking {
            try {
                val result = webService.crawlMyPageData()
                
                SwingUtilities.invokeLater {
                    if (result.success) {
                        // 显示工具窗口
                        val toolWindowManager = ToolWindowManager.getInstance(project)
                        val toolWindow = toolWindowManager.getToolWindow("SW Cares")
                        toolWindow?.show()
                        
                        Messages.showInfoMessage(
                            project,
                            "成功爬取到 ${result.records.size} 条记录",
                            "爬取成功"
                        )
                    } else {
                        Messages.showErrorDialog(
                            project,
                            "数据爬取失败: ${result.message}",
                            "爬取错误"
                        )
                    }
                }
            } catch (e: Exception) {
                logger.error("Error crawling data", e)
                SwingUtilities.invokeLater {
                    Messages.showErrorDialog(
                        project,
                        "爬取数据时发生错误: ${e.message}",
                        "错误"
                    )
                }
            }
        }
    }
}
