plugins {
  id("java")
  id("org.jetbrains.kotlin.jvm") version "1.9.10"
  id("org.jetbrains.intellij") version "1.17.3"
}

group = "qhyu.asia"
version = "1.0-SNAPSHOT"

repositories {
  mavenCentral()
}

dependencies {
  // HTTP client for web requests
  implementation("com.squareup.okhttp3:okhttp:4.12.0")
  // JSON parsing
  implementation("com.google.code.gson:gson:2.10.1")
  // HTML parsing
  implementation("org.jsoup:jsoup:1.17.2")
}

intellij {
  version.set("2023.2.5")
  type.set("IC")
}

tasks {
  patchPluginXml {
    sinceBuild.set("232")
    untilBuild.set("242.*")

    changeNotes.set("""
      Initial version with smart login detection
    """.trimIndent())
  }

  // Set the JVM compatibility versions
  withType<JavaCompile> {
    sourceCompatibility = "17"
    targetCompatibility = "17"
  }
  withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    kotlinOptions.jvmTarget = "17"
  }
}
