plugins {
  id("java")
  id("org.jetbrains.kotlin.jvm") version "1.9.10"
  id("org.jetbrains.intellij") version "1.17.3"
}

group = "qhyu.asia"
version = "1.0-SNAPSHOT"

repositories {
  mavenCentral()
}

dependencies {
  // HTTP client for web requests
  implementation("com.squareup.okhttp3:okhttp:4.12.0")
  // JSON parsing
  implementation("com.google.code.gson:gson:2.10.1")
  // HTML parsing
  implementation("org.jsoup:jsoup:1.17.2")
  // Coroutines for async operations
  implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
  implementation("org.jetbrains.kotlinx:kotlinx-coroutines-swing:1.7.3")
}

intellijPlatform {
  pluginConfiguration {
    ideaVersion {
      sinceBuild = "251"
    }

    changeNotes = """
      Initial version
    """.trimIndent()
  }
}

tasks {
  // Set the JVM compatibility versions
  withType<JavaCompile> {
    sourceCompatibility = "21"
    targetCompatibility = "21"
  }
  withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    kotlinOptions.jvmTarget = "21"
  }
}
